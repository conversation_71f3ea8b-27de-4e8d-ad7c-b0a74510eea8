-- 为 user_interactions 表添加唯一约束，防止重复的进度记录
-- 这样每个用户每个内容每种交互类型只能有一条记录

-- 首先清理重复的 watch_progress 记录，只保留最新的
WITH ranked_progress AS (
    SELECT 
        id,
        ROW_NUMBER() OVER (
            PARTITION BY user_id, content_type, content_id, interaction_type 
            ORDER BY created_at DESC
        ) as rn
    FROM user_interactions 
    WHERE interaction_type = 'watch_progress'
)
DELETE FROM user_interactions 
WHERE id IN (
    SELECT id FROM ranked_progress WHERE rn > 1
);

-- 添加唯一约束
ALTER TABLE user_interactions 
ADD CONSTRAINT uq_user_content_interaction 
UNIQUE (user_id, content_type, content_id, interaction_type);

-- 添加复合索引以提高查询性能
CREATE INDEX IF NOT EXISTS ix_user_interactions_lookup 
ON user_interactions (user_id, content_type, content_id, interaction_type);

-- 为 created_at 和 updated_at 添加索引
CREATE INDEX IF NOT EXISTS ix_user_interactions_created_at 
ON user_interactions (created_at);

-- 添加 updated_at 字段（如果不存在）
ALTER TABLE user_interactions 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 创建触发器自动更新 updated_at
CREATE OR REPLACE FUNCTION update_user_interactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_user_interactions_updated_at
    BEFORE UPDATE ON user_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_interactions_updated_at();
