from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api.deps import get_current_user, get_current_user_optional
from app.api.permission_deps import require_permission
from app.core.limiter import limiter
from app.core.logging import logger
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.core.permission_system import Action, PermissionChecker, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.db.session import get_db
from app.models.user import User, UserRole
from app.services.article_aggregation_service import ArticleAggregationService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.password_service import PasswordHashService
from app.services.scratch_aggregation_service import ScratchAggregationService
from app.services.service_factory import (
    get_article_aggregation_service,
    get_password_service,
    get_scratch_aggregation_service,
    get_token_service,
    get_user_aggregation_service,
    get_user_cache_service,
    get_video_aggregation_service,
)
from app.services.user_aggregation_service import UserAggregationService
from app.services.user_cache_service import UserCacheService
from app.services.video_aggregation_service import VideoAggregationService
from app.tasks.cache_tasks import task_invalidate_user_cache

router = APIRouter()


@router.get("/", response_model=CursorPaginationResponse[schemas.UserAggregated])
async def get_users(
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("id", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.READ, scope=Scope.ALL)
        )
    ),
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
) -> Any:
    """获取用户列表"""
    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    return await user_aggregation_service.get_paginated_users(db=db, params=pagination_params)


@router.get("/me", response_model=schemas.UserAggregated)
@limiter.limit("100/minute")
async def get_current_user_info(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
) -> Any:
    """获取当前用户的详细信息"""
    user = await user_aggregation_service.get_user_profile(db=db, user_id=current_user.id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    return user


@router.get("/mentions", response_model=schemas.UserMentionSearchResponse)
@limiter.limit("60/minute")
async def search_mention_candidates(
    request: Request,
    q: str = Query(..., description="搜索关键字（用户名或昵称）", min_length=1),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量，默认10，最大50"),
    cursor: str | None = Query(None, description="分页游标，用于加载更多结果"),
    db: AsyncSession = Depends(get_db),
    current_user: User | None = Depends(get_current_user_optional),
) -> schemas.UserMentionSearchResponse:
    """
    根据关键字搜索可提及的用户列表

    业务规则：
    - 搜索匹配用户名和昵称
    - 只返回激活状态的用户
    - 支持模糊搜索
    - 结果按相关度排序
    - 过滤掉被当前用户屏蔽的用户（如果已登录）
    - 过滤掉屏蔽了当前用户的用户（如果已登录）
    """
    try:
        # 构建基础查询
        from sqlalchemy import and_, func, or_

        query = select(models.User).where(
            and_(
                models.User.is_active == True,  # noqa: E712
                or_(
                    func.lower(models.User.username).contains(q.lower()),
                    func.lower(models.User.nickname).contains(q.lower()),
                ),
            )
        )

        # 如果有游标，添加分页条件
        if cursor:
            try:
                cursor_id = int(cursor)
                query = query.where(models.User.id < cursor_id)
            except ValueError:
                pass  # 忽略无效游标

        # 按相关度排序（优先匹配用户名，然后是昵称）
        query = query.order_by(
            # 用户名完全匹配优先
            func.lower(models.User.username) == q.lower(),
            # 昵称完全匹配次之
            func.lower(models.User.nickname) == q.lower(),
            # 然后按ID降序
            models.User.id.desc(),
        ).limit(limit + 1)  # 多查一个用于判断是否有下一页

        result = await db.execute(query)
        users = result.scalars().all()

        # 判断是否有下一页
        has_next = len(users) > limit
        if has_next:
            users = users[:limit]

        # 构建响应数据
        items = []
        for user in users:
            # 确定显示名称（优先昵称，其次用户名）
            display_name = user.nickname if user.nickname else user.username

            items.append(
                schemas.UserMentionCandidate(
                    id=str(user.id),
                    display_name=display_name,
                    avatar_url=user.avatar,
                )
            )

        # 设置下一页游标
        next_cursor = None
        if has_next and users:
            next_cursor = str(users[-1].id)

        # 获取总数（简化版本，实际可能需要缓存）
        count_query = select(func.count(models.User.id)).where(
            and_(
                models.User.is_active == True,  # noqa: E712
                or_(
                    func.lower(models.User.username).contains(q.lower()),
                    func.lower(models.User.nickname).contains(q.lower()),
                ),
            )
        )
        count_result = await db.execute(count_query)
        total_count = count_result.scalar() or 0

        return schemas.UserMentionSearchResponse(
            items=items, next_cursor=next_cursor, total_count=total_count
        )

    except Exception as e:
        logger.error(f"搜索提及候选用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="搜索用户失败"
        ) from e


@router.get("/{user_id}", response_model=schemas.UserAggregated)
async def get_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
) -> Any:
    """获取特定用户的详细信息"""
    user = await user_aggregation_service.get_user_profile(db=db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    return user


@router.put("/{user_id}", response_model=schemas.User)
async def update_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int = Path(..., description="用户 ID（必须为整数）"),
    user_in: schemas.UserUpdate,
    current_user: User = Depends(get_current_user),
    password_service: PasswordHashService = Depends(get_password_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> Any:
    """更新用户信息"""
    user = await crud.user.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查权限：用户只能更新自己的信息，或者拥有管理权限
    if user.id != current_user.id:
        # 如果不是更新自己的信息，需要检查是否有管理权限
        await PermissionChecker.require_permission(
            db,
            current_user,
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL),
        )
    # 如果是更新自己的信息，直接允许（不需要额外权限检查）

    # 检查是否有权限更新超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权更新超级管理员",
        )

    # 检查邮箱是否已存在
    if user_in.email and user_in.email != user.email:
        result = await db.execute(select(User).where(User.email == user_in.email))
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 检查角色是否存在
    if user_in.role_id and user_in.role_id != user.role_id:
        result = await db.execute(select(UserRole).where(UserRole.id == user_in.role_id))
        role = result.scalar_one_or_none()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色不存在",
            )

    # 更新用户信息 - 使用优雅的部分更新方式
    update_data = user_in.model_dump(exclude_unset=True)

    # 特殊处理密码字段
    if "password" in update_data and update_data["password"]:
        update_data["password"] = password_service.get_password_hash(update_data["password"])
    else:
        update_data.pop("password", None)

    # 批量更新字段
    for field, value in update_data.items():
        setattr(user, field, value)

    await db.commit()
    await db.refresh(user)

    # 更新后清除缓存
    await user_cache_service.invalidate_entity(user_id)

    return user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.DELETE, scope=Scope.ALL)
        )
    ),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> None:
    """删除用户（采用延迟双删策略）"""
    user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否有权限删除超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除超级管理员",
        )

    # 不允许删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户",
        )

    # 第一次删除：立即删除缓存
    await user_cache_service.invalidate_entity(user_id)

    # 软删除用户
    await crud.user.remove(db=db, id=user_id)

    # 第二次删除：延迟异步删除缓存
    task_invalidate_user_cache.apply_async(args=[user_id], countdown=1)


# ==================== 管理员用户封禁管理接口 ====================


@router.post("/admin/ban")
async def ban_user_admin(
    request_body: schemas.UserBanRequest,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL)
        )
    ),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    http_request: Request = None,
) -> dict:
    """封禁用户（管理员）

    封禁后将：
    1. 设置用户为未激活状态
    2. 撤销用户所有 token（强制下线）
    3. 记录审计日志
    """
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    try:
        # 查找用户 - 使用 crud 获取 SQLAlchemy model 以便修改
        user = await crud.user.get(db, id=request_body.user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 检查是否有权限封禁超级管理员
        if user.is_superuser and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权封禁超级管理员",
            )

        # 不允许封禁自己
        if user.id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能封禁自己的账户",
            )

        # 检查用户是否已被封禁
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已被封禁",
            )

        # 记录操作前的值
        old_values = {"is_active": user.is_active}

        # 1. 更新用户状态
        user.is_active = False
        await db.commit()
        await db.refresh(user)

        # 2. 撤销所有 token（强制下线）
        revoked_count = await token_service.revoke_user_tokens(user.username)

        # 3. 清除用户缓存
        await user_cache_service.invalidate_entity(user.id)

        # 4. 记录审计日志
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BAN,
            resource_type=AuditResourceType.USER,
            resource_id=user.id,
            resource_name=user.username,
            description="封禁用户",
            reason=request_body.reason,
            old_values=old_values,
            new_values={
                "is_active": False,
                "revoked_tokens": revoked_count,
                "ban_reason": request_body.reason,
            },
            request=http_request,
        )

        logger.info(
            f"管理员 {current_user.id} 封禁了用户 {user.id}，撤销了 {revoked_count} 个 token"
        )

        return {
            "message": "用户封禁成功",
            "user_id": user.id,
            "username": user.username,
            "revoked_tokens": revoked_count,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"封禁用户失败：{str(e)}")
        raise HTTPException(status_code=500, detail="封禁用户失败") from e


@router.post("/admin/unban")
async def unban_user_admin(
    request_body: schemas.UserUnbanRequest,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL)
        )
    ),
    db: AsyncSession = Depends(get_db),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    http_request: Request = None,
) -> dict:
    """解除用户封禁（管理员）

    解封后用户可以正常登录
    """
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    try:
        # 查找用户 - 使用 crud 获取 SQLAlchemy model 以便修改
        user = await crud.user.get(db, id=request_body.user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 检查用户是否已被封禁
        if user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未被封禁",
            )

        # 记录操作前的值
        old_values = {"is_active": user.is_active}

        # 1. 更新用户状态
        user.is_active = True
        await db.commit()
        await db.refresh(user)

        # 2. 清除用户缓存
        await user_cache_service.invalidate_entity(user.id)

        # 3. 记录审计日志
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.UPDATE,
            resource_type=AuditResourceType.USER,
            resource_id=user.id,
            resource_name=user.username,
            description="解除用户封禁",
            reason=request_body.reason,
            old_values=old_values,
            new_values={"is_active": True, "unban_reason": request_body.reason},
            request=http_request,
        )

        logger.info(f"管理员 {current_user.id} 解除了用户 {user.id} 的封禁")

        return {"message": "用户解除封禁成功", "user_id": user.id, "username": user.username}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解除用户封禁失败：{str(e)}")
        raise HTTPException(status_code=500, detail="解除用户封禁失败") from e


@router.post("/admin/batch-ban")
async def batch_ban_users_admin(
    user_ids: list[int],
    reason: str,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL)
        )
    ),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    http_request: Request = None,
) -> dict:
    """批量封禁用户（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    banned_count = 0
    banned_users = []
    total_revoked_tokens = 0

    for user_id in user_ids:
        try:
            # 查找用户 - 使用 crud 获取 SQLAlchemy model 以便修改
            user = await crud.user.get(db, id=user_id)

            # 跳过不存在、已封禁、超级管理员、自己的用户
            if not user:
                logger.warning(f"批量封禁：用户 {user_id} 不存在")
                continue
            if not user.is_active:
                logger.warning(f"批量封禁：用户 {user_id} 已被封禁")
                continue
            if user.is_superuser and not current_user.is_superuser:
                logger.warning(f"批量封禁：无权封禁超级管理员 {user_id}")
                continue
            if user.id == current_user.id:
                logger.warning(f"批量封禁：不能封禁自己 {user_id}")
                continue

            # 封禁用户
            user.is_active = False
            await db.commit()

            # 撤销 token
            revoked_count = await token_service.revoke_user_tokens(user.username)
            total_revoked_tokens += revoked_count

            # 清除缓存
            await user_cache_service.invalidate_entity(user.id)

            banned_count += 1
            banned_users.append(
                {"id": user_id, "username": user.username, "revoked_tokens": revoked_count}
            )

        except Exception as e:
            logger.error(f"批量封禁用户 {user_id} 失败：{str(e)}")
            continue

    # 记录批量操作审计日志
    if banned_users:
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BATCH_UPDATE,
            resource_type=AuditResourceType.USER,
            description=f"批量封禁 {banned_count} 个用户",
            reason=reason,
            new_values={
                "action": "ban",
                "user_ids": user_ids,
                "banned_count": banned_count,
                "banned_users": banned_users,
                "total_revoked_tokens": total_revoked_tokens,
            },
            request=http_request,
        )

    logger.info(
        f"管理员 {current_user.id} 批量封禁了 {banned_count} 个用户，撤销了 {total_revoked_tokens} 个 token"
    )

    return {
        "message": f"成功封禁 {banned_count} 个用户",
        "banned_count": banned_count,
        "total_revoked_tokens": total_revoked_tokens,
    }


@router.post("/admin/batch-unban")
async def batch_unban_users_admin(
    user_ids: list[int],
    reason: str | None = None,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL)
        )
    ),
    db: AsyncSession = Depends(get_db),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    http_request: Request = None,
) -> dict:
    """批量解除用户封禁（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    unbanned_count = 0
    unbanned_users = []

    for user_id in user_ids:
        try:
            # 查找用户 - 使用 crud 获取 SQLAlchemy model 以便修改
            user = await crud.user.get(db, id=user_id)

            # 跳过不存在或未被封禁的用户
            if not user:
                logger.warning(f"批量解封：用户 {user_id} 不存在")
                continue
            if user.is_active:
                logger.warning(f"批量解封：用户 {user_id} 未被封禁")
                continue

            # 解封用户
            user.is_active = True
            await db.commit()

            # 清除缓存
            await user_cache_service.invalidate_entity(user.id)

            unbanned_count += 1
            unbanned_users.append({"id": user_id, "username": user.username})

        except Exception as e:
            logger.error(f"批量解封用户 {user_id} 失败：{str(e)}")
            continue

    # 记录批量操作审计日志
    if unbanned_users:
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BATCH_UPDATE,
            resource_type=AuditResourceType.USER,
            description=f"批量解除 {unbanned_count} 个用户封禁",
            reason=reason,
            new_values={
                "action": "unban",
                "user_ids": user_ids,
                "unbanned_count": unbanned_count,
                "unbanned_users": unbanned_users,
            },
            request=http_request,
        )

    logger.info(f"管理员 {current_user.id} 批量解除了 {unbanned_count} 个用户封禁")

    return {"message": f"成功解除 {unbanned_count} 个用户封禁", "unbanned_count": unbanned_count}


@router.post("/follow")
async def follow_user(
    *,
    db: AsyncSession = Depends(get_db),
    follow_data: schemas.UserFollow,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.FOLLOW, scope=Scope.ALL)
        )
    ),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> dict:
    """关注用户（事件驱动）"""
    if follow_data.user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能关注自己",
        )

    followed_user = await user_cache_service.get_entity(db, entity_id=follow_data.user_id)
    if not followed_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="被关注的用户不存在",
        )

    if followed_user in current_user.following:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="已经关注了该用户",
        )

    await crud.user.follow(db, follower=current_user, followed=followed_user)
    return {"message": "关注成功"}


@router.post("/{user_id}/unfollow", status_code=status.HTTP_204_NO_CONTENT)
async def unfollow_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int = Path(..., description="用户 ID（必须为整数）"),
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.FOLLOW, scope=Scope.ALL)
        )
    ),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> None:
    """取消关注用户（事件驱动）"""
    followed_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not followed_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="被取消关注的用户不存在",
        )

    if followed_user not in current_user.following:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="未关注该用户",
        )

    await crud.user.unfollow(db, follower=current_user, followed=followed_user)


@router.get(
    "/{user_id}/likes/articles",
    response_model=CursorPaginationResponse[schemas.ArticleOut],
    summary="获取用户点赞的文章列表",
)
async def get_user_liked_articles(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("like_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """
    获取指定用户点赞的文章列表。
    - 默认按点赞时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    # 1. 检查目标用户是否存在
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限检查
    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_author and not is_admin and not target_user.check_privacy_setting("likes", "article"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的点赞文章"
        )

    # 3. 调用新的高效分页函数
    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.article.get_paginated_liked_articles(
        db=db, user_id=user_id, params=pagination_params
    )

    # 4. 聚合数据并返回
    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    article_ids = [item.id for item in paginated_result.items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [articles_map[id] for id in article_ids if id in articles_map]

    return CursorPaginationResponse(
        items=sorted_articles,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/history/articles",
    response_model=CursorPaginationResponse[schemas.ArticleOut],
    summary="获取用户浏览历史的文章列表",
)
async def get_user_history_articles(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("history_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """
    获取指定用户浏览历史的文章列表。
    - 默认按浏览时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    # 1. 检查目标用户是否存在
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限检查：只允许用户本人或管理员查看历史记录
    is_owner = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_owner and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的浏览历史"
        )

    # 3. 调用新的高效分页函数
    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.article.get_paginated_history_articles(
        db=db, user_id=user_id, params=pagination_params
    )

    # 4. 聚合数据并返回
    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    article_ids = [item.id for item in paginated_result.items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [articles_map[id] for id in article_ids if id in articles_map]

    return CursorPaginationResponse(
        items=sorted_articles,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/favorites/articles",
    response_model=CursorPaginationResponse[schemas.ArticleOut],
    summary="获取用户收藏的文章列表",
)
async def get_user_favorite_articles(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("favorite_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """
    获取指定用户收藏的文章列表。
    - 默认按收藏时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    # 1. 检查目标用户是否存在
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限检查
    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if (
        not is_author
        and not is_admin
        and not target_user.check_privacy_setting("favorites", "article")
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的收藏文章"
        )

    # 3. 调用新的高效分页函数
    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.article.get_paginated_favorite_articles(
        db=db, user_id=user_id, params=pagination_params
    )

    # 4. 聚合数据并返回
    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    article_ids = [item.id for item in paginated_result.items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [articles_map[id] for id in article_ids if id in articles_map]

    return CursorPaginationResponse(
        items=sorted_articles,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/likes/videos",
    response_model=CursorPaginationResponse[schemas.VideoOut],
    summary="获取用户点赞的视频列表",
)
async def get_user_liked_videos(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("like_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定用户点赞的视频列表。
    - 默认按点赞时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_author and not is_admin and not target_user.check_privacy_setting("likes", "video"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的点赞视频"
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.video.get_paginated_liked_videos(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    video_ids = [item.id for item in paginated_result.items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db, video_ids=video_ids, current_user=current_user
    )

    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[id] for id in video_ids if id in videos_map]

    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/likes/scratch",
    response_model=CursorPaginationResponse[schemas.ScratchProductOut],
    summary="获取用户点赞的Scratch项目列表",
)
async def get_user_liked_scratch_projects(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("like_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    scratch_aggregation_service: ScratchAggregationService = Depends(
        get_scratch_aggregation_service
    ),
) -> Any:
    """获取指定用户点赞的 Scratch 项目列表。"""

    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_author and not is_admin and not target_user.check_privacy_setting("likes", "scratch"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您没有权限查看该用户的点赞Scratch项目",
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.scratch_product.get_paginated_liked_projects(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    project_ids = [item.project_id for item in paginated_result.items]
    aggregated_projects = await scratch_aggregation_service.get_projects_by_ids(
        db=db,
        project_ids=project_ids,
        current_user=current_user,
    )

    projects_map = {project.project_id: project for project in aggregated_projects}
    sorted_projects = [projects_map[pid] for pid in project_ids if pid in projects_map]

    return CursorPaginationResponse(
        items=sorted_projects,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/history/scratch",
    response_model=CursorPaginationResponse[schemas.ScratchProductOut],
    summary="获取用户浏览历史的Scratch项目列表",
)
async def get_user_history_scratch_projects(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("history_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> Any:
    """
    获取指定用户浏览历史的Scratch项目列表。
    - 默认按浏览时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_owner = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_owner and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的浏览历史"
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.scratch_product.get_paginated_history_projects(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    # 返回已分页的项目列表（已从数据库关联查询中获取完整信息）
    return paginated_result


@router.get(
    "/{user_id}/favorites/scratch",
    response_model=CursorPaginationResponse[schemas.ScratchProductOut],
    summary="获取用户收藏的Scratch项目列表",
)
async def get_user_favorite_scratch_projects(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("favorite_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    scratch_aggregation_service: ScratchAggregationService = Depends(
        get_scratch_aggregation_service
    ),
) -> Any:
    """获取指定用户收藏的 Scratch 项目列表。"""

    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if (
        not is_author
        and not is_admin
        and not target_user.check_privacy_setting("favorites", "scratch")
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您没有权限查看该用户的收藏Scratch项目",
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.scratch_product.get_paginated_favorite_projects(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    project_ids = [item.project_id for item in paginated_result.items]
    aggregated_projects = await scratch_aggregation_service.get_projects_by_ids(
        db=db,
        project_ids=project_ids,
        current_user=current_user,
    )

    projects_map = {project.project_id: project for project in aggregated_projects}
    sorted_projects = [projects_map[pid] for pid in project_ids if pid in projects_map]

    return CursorPaginationResponse(
        items=sorted_projects,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/favorites/videos",
    response_model=CursorPaginationResponse[schemas.VideoOut],
    summary="获取用户收藏的视频列表",
)
async def get_user_favorite_videos(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("favorite_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定用户收藏的视频列表。
    - 默认按收藏时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if (
        not is_author
        and not is_admin
        and not target_user.check_privacy_setting("favorites", "video")
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的收藏视频"
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.video.get_paginated_favorite_videos(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    video_ids = [item.id for item in paginated_result.items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db, video_ids=video_ids, current_user=current_user
    )

    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[id] for id in video_ids if id in videos_map]

    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )


@router.get(
    "/{user_id}/history/videos",
    response_model=CursorPaginationResponse[schemas.VideoOut],
    summary="获取用户浏览历史的视频列表",
)
async def get_user_history_videos(
    user_id: int,
    *,
    db: AsyncSession = Depends(get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("history_time", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    current_user: models.User | None = Depends(get_current_user_optional),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定用户浏览历史的视频列表。
    - 默认按浏览时间倒序排列。
    - 支持游标分页。
    - 遵循用户隐私设置。
    """
    target_user = await user_cache_service.get_entity(db, entity_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    is_owner = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    if not is_owner and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="您没有权限查看该用户的浏览历史"
        )

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )
    paginated_result = await crud.video.get_paginated_history_videos(
        db=db, user_id=user_id, params=pagination_params
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    video_ids = [item.id for item in paginated_result.items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db, video_ids=video_ids, current_user=current_user
    )

    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[id] for id in video_ids if id in videos_map]

    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
    )
