from typing import Any

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, Request, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.core.limiter import limiter
from app.core.logging import logger
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse, CursorPaginator
from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.schemas.video import VideoOut
from app.services.behavior_tracking_service import BehaviorTrackingService
from app.services.recommendation_service import RecommendationService
from app.services.service_factory import (
    get_behavior_tracking_service,
    get_recommendation_service,
    get_video_aggregation_service,
    get_video_folder_service,
)
from app.services.video_aggregation_service import VideoAggregationService
from app.services.video_folder_service import VideoFolderService
from app.utils.pagination_adapter import PaginationAdapter

router = APIRouter()


@router.get("/recommendations", response_model=CursorPaginationResponse[VideoOut])
async def get_video_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取视频推荐列表。

    - **权限**: 登录用户。
    - **逻辑**: 调用推荐服务获取分页后的视频推荐。
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="需要登录才能获取个性化推荐"
        )

    # 1. 将游标转换为页码
    page = PaginationAdapter.cursor_to_page(pagination.cursor)

    # 2. 从推荐服务获取个性化推荐
    recommendation_items = await recommendation_service.get_paginated_content_recommendations(
        user_id=current_user.id,
        content_type="video",
        page=page,
        limit=pagination.size,
    )

    if not recommendation_items:
        return PaginationAdapter.empty_cursor_response()

    # 3. 聚合数据
    video_ids = [item.content_id for item in recommendation_items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 4. 保持推荐顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[vid] for vid in video_ids if vid in videos_map]

    # 5. 使用适配器构建游标分页响应
    has_more_items = len(recommendation_items) == pagination.size
    return PaginationAdapter.adapt_page_based_to_cursor(
        items=sorted_videos,
        pagination=pagination,
        has_more_items=has_more_items,
    )


@router.get("/{video_id}/similar", response_model=CursorPaginationResponse[VideoOut])
async def get_similar_videos(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取相似视频列表。

    - **权限**: 公开。
    - **逻辑**: 调用推荐服务获取相似内容。
    """
    # 1. 检查源视频是否存在
    source_video = await crud.video.get(db=db, id=video_id)
    if not source_video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="源视频不存在")

    # 2. 从推荐服务获取相似内容
    similar_items = await recommendation_service.get_similar_content(
        content_type="video", content_id=video_id, limit=pagination.size
    )

    if not similar_items:
        return PaginationAdapter.empty_cursor_response()

    # 3. 聚合数据
    video_ids = [item.content_id for item in similar_items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 4. 保持推荐顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[vid] for vid in video_ids if vid in videos_map]

    # 5. 构建并返回响应 (相似内容通常不分页，但保持一致的响应格式)
    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=len(sorted_videos),
        has_next=False,
        has_previous=False,
        next_cursor=None,
        previous_cursor=None,
    )


@router.get("/hot", response_model=CursorPaginationResponse[VideoOut])
async def get_hot_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取热门视频列表（分页）
    """
    # 1. 将游标转换为偏移量
    offset = PaginationAdapter.cursor_to_offset(pagination.cursor)

    # 2. 从推荐服务获取热门内容
    paginated_result = await recommendation_service.get_hot_content(
        content_type="video", limit=pagination.size, offset=offset
    )

    if not paginated_result.items:
        return PaginationAdapter.empty_cursor_response()

    # 3. 聚合数据
    video_ids = [item.content_id for item in paginated_result.items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 4. 保持推荐顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[video_id] for video_id in video_ids if video_id in videos_map]

    # 5. 使用适配器构建游标分页响应
    return PaginationAdapter.adapt_recommendation_to_cursor(
        items=sorted_videos,
        pagination=pagination,
        recommendation_result=paginated_result,
    )


@router.get(
    "/category/{category_id}",
    response_model=CursorPaginationResponse[VideoOut],
    summary="获取指定分类下的视频列表",
)
async def get_category_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    category_id: int = Path(..., description="分类ID (设为0表示获取所有分类)"),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定分类下的视频列表，并进行分页。

    - **权限**: 公开接口，游客和登录用户均可访问。
    - **过滤**: 仅返回已发布、已审核且未删除的视频。
    - **分页**: 支持高效的游标分页。
    - **特殊处理**: 当category_id为0时，返回所有已发布且已审核的视频。
    """
    # 准备过滤条件
    filters = {}
    if category_id != 0:
        # 检查分类是否存在
        category = await crud.category.get(db=db, id=category_id)
        if not category:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="分类不存在")
        filters["category_id"] = category_id

    # 1. 从 CRUD 层获取分页后的视频ID
    paginated_ids_response = await crud.video.get_paginated_video_ids(
        db=db,
        params=pagination,
        filters=filters if filters else None,
        access_level="public",
    )

    video_ids = paginated_ids_response.items
    if not video_ids:
        return CursorPaginationResponse(items=[], **paginated_ids_response.dict(exclude={"items"}))

    # 2. 使用聚合服务获取完整的视频信息
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 3. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=aggregated_videos,
        total_count=paginated_ids_response.total_count,
        has_next=paginated_ids_response.has_next,
        has_previous=paginated_ids_response.has_previous,
        next_cursor=paginated_ids_response.next_cursor,
        previous_cursor=paginated_ids_response.previous_cursor,
    )


@router.get(
    "/users/{user_id}/folders",
    response_model=CursorPaginationResponse[schemas.VideoFolder],
    summary="获取指定用户的文件夹列表",
)
async def get_user_folders(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    include_video_count: bool = Query(True, description="是否包含视频数量统计"),
    include_children: bool = Query(False, description="是否包含子文件夹信息"),
    include_recent_videos: bool = Query(
        False, description="是否包含每个文件夹的最新视频预览（最多3个）"
    ),
    include_default_folder: bool = Query(True, description="是否包含默认文件夹"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取指定用户的文件夹列表

    **权限控制**：
    - **用户本人**：可以访问所有文件夹（包括私有文件夹）
    - **其他用户**：只能访问公开文件夹
    - **未登录用户**：只能访问公开文件夹

    **功能特性**：
    - 支持游标分页，提高大量数据加载性能
    - 可选择是否包含视频数量统计
    - 可选择是否包含子文件夹信息
    - 可选择是否包含最新视频预览
    - 可选择是否包含默认文件夹
    - 自动权限过滤

    **分页参数**：
    - **cursor**: 游标位置，用于获取下一页数据
    - **size**: 每页大小，范围1-100
    - **order_by**: 排序字段，默认为id
    - **order_direction**: 排序方向，asc或desc
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        logger.error(f"[文件夹调试] 目标用户不存在 - user_id: {user_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限判断
    is_own_folders = current_user and current_user.id == user_id
    logger.info(f"[文件夹调试] 权限判断 - is_own_folders: {is_own_folders}")

    # 权限检查：如果是访问他人的文件夹，需要有相应权限或只能看公开的
    if not is_own_folders:
        # 检查是否有查看所有文件夹的权限
        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            Permission(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        # 如果没有管理权限，只能查看公开文件夹（后续会过滤）
        if not has_view_all_permission:
            user_info = current_user.id if current_user else "anonymous"
            logger.info(f"[文件夹调试] 用户 {user_info} 访问用户 {user_id} 的公开文件夹")
        logger.info(
            f"[文件夹调试] 权限检查完成 - has_view_all_permission: {has_view_all_permission}"
        )

    # 3. 构建查询获取用户的文件夹列表
    query = select(crud.video_folder.model).where(
        crud.video_folder.model.user_id == user_id,
        crud.video_folder.model.is_deleted.is_(False),
    )

    # 是否包含默认文件夹的筛选
    if not include_default_folder:
        query = query.where(crud.video_folder.model.is_default.is_(False))

    # 4. 权限过滤：非本人只能看到公开文件夹
    if not is_own_folders:
        # 修正查询条件 - 使用access_level而不是is_public属性
        query = query.where(crud.video_folder.model.access_level >= 1)
    paginated_result = await CursorPaginator.paginate_query(
        db=db,
        query=query,
        params=pagination,
        cursor_field=pagination.order_by,
        include_total=include_total,
    )
    folders = paginated_result.items
    # 6. 补充文件夹信息
    for folder in folders:
        if include_video_count:
            # 获取文件夹中的视频数量
            from sqlalchemy import func

            video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(
                        crud.video.model.folder_id == folder.id,
                        crud.video.model.is_deleted.is_(False),
                    )
                )
            ).scalar()
            # 使用update_video_count方法更新视频数量，而不是直接设置属性
            folder.update_video_count(video_count)

        if include_children:
            # 检查是否有子文件夹（通过parent_id）
            children_query = select(crud.video_folder.model).where(
                crud.video_folder.model.parent_id == folder.id,
                crud.video_folder.model.is_deleted.is_(False),
            )

            # 如果不是本人，也要过滤子文件夹的公开性
            if not is_own_folders:
                children_query = children_query.where(crud.video_folder.model.is_public.is_(True))

            result = await db.execute(children_query)
            children = result.scalars().all()
            folder.has_children = bool(children)

        if include_recent_videos:
            # 获取文件夹中最新的3个视频作为预览
            video_query = select(crud.video.model).where(
                crud.video.model.folder_id == folder.id,
                crud.video.model.is_deleted.is_(False),
            )

            # 根据权限过滤视频
            if not is_own_folders:
                video_query = video_query.where(
                    crud.video.model.is_published.is_(True),
                    crud.video.model.is_approved.is_(True),
                )

            video_query = video_query.order_by(crud.video.model.created_at.desc()).limit(3)

            result = await db.execute(video_query)
            recent_videos = result.scalars().all()

            # 将视频信息添加到文件夹对象（需要扩展 schema）
            folder.recent_videos = [
                {
                    "id": video.id,
                    "title": video.title,
                    "cover_url": video.cover_url,
                    "duration": video.duration,
                    "created_at": video.created_at,
                }
                for video in recent_videos
            ]

    # 7. 构造新的分页响应并返回
    response = CursorPaginationResponse(
        items=folders,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )

    return response


@router.get(
    "/folders/{folder_id}/videos",
    response_model=CursorPaginationResponse[VideoOut],
    summary="获取指定文件夹下的视频列表 (重构版)",
)
async def get_folder_videos(
    folder_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    cursor: int = Query(0, description="游标位置，用于分页"),
    size: int = Query(20, description="每页大小", ge=1, le=100),
    video_status: str | None = Query(
        None,
        alias="status",
        description="视频状态筛选 (draft, pending, rejected, approved)",
    ),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定文件夹下的视频列表

    **权限控制**:
    - **文件夹所有者**: 可以访问所有状态的视频。
    - **其他用户**: 只能访问公开文件夹中已发布且已审核通过的视频。

    **功能特性**:
    - 使用 VideoAggregationService 进行数据聚合
    - 支持游标分页
    - 支持状态过滤
    - 自动权限过滤和数据聚合
    """
    # 1. 检查文件夹是否存在
    folder = await crud.video_folder.get(db=db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文件夹不存在")

    # 2. 权限检查
    is_own_folder = current_user and current_user.id == folder.user_id
    is_admin = current_user and current_user.is_superuser
    can_view_all = is_own_folder or is_admin

    # 如果不是文件夹所有者，检查文件夹是否公开
    if not can_view_all and not folder.is_public:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权访问此文件夹")

    # 检查 status 参数的使用权限
    if video_status and not can_view_all:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="没有权限查看草稿或待审核视频"
        )

    # 3. 调用 VideoAggregationService 获取视频列表
    result = await video_aggregation_service.get_videos_by_folder(
        db=db,
        folder_id=folder_id,
        cursor=cursor,
        size=size,
        status=video_status,
        current_user=current_user,
    )

    return result


# ==================== 管理员视频管理接口 ====================
# 注意：管理员路由必须放在 /{video_id} 之前，避免路由冲突


@router.get("/admin", response_model=CursorPaginationResponse[VideoOut])
async def get_videos_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),  # 需要添加权限检查
    pagination: CursorPaginationParams = Depends(),
    status: str | None = Query(None, description="视频状态过滤"),
    author_id: int | None = Query(None, description="作者ID过滤"),
    category_id: int | None = Query(None, description="分类ID过滤"),
    keyword: str | None = Query(None, description="关键词搜索"),
    start_date: str | None = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: str | None = Query(None, description="结束日期 YYYY-MM-DD"),
) -> CursorPaginationResponse[VideoOut]:
    """获取视频列表（管理员）- 支持多条件过滤"""
    from datetime import datetime

    from sqlalchemy import and_, or_, select

    from app.models.review import ContentType as ReviewContentType

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    # 构建查询条件
    conditions: list[Any] = [models.Video.is_deleted.is_(False)]

    review_status_subquery = (
        select(models.Review.status)
        .where(
            models.Review.content_type == ReviewContentType.VIDEO.value,
            models.Review.content_id == models.Video.id,
        )
        .order_by(models.Review.updated_at.desc(), models.Review.id.desc())
        .limit(1)
        .scalar_subquery()
    )

    if status:
        try:
            status_enum = schemas.VideoStatus(status)
        except ValueError as exc:
            raise HTTPException(status_code=400, detail="视频状态参数无效") from exc

        if status_enum == schemas.VideoStatus.ALL:
            # 管理员查询全部状态，保留基础条件即可
            pass
        elif status_enum == schemas.VideoStatus.DRAFT:
            conditions.extend(
                [
                    models.Video.is_published.is_(False),
                ]
            )
        elif status_enum == schemas.VideoStatus.PUBLISHED_APPROVED:
            conditions.extend(
                [
                    models.Video.is_published.is_(True),
                    models.Video.is_approved.is_(True),
                    review_status_subquery == models.ReviewStatus.APPROVED.value,
                ]
            )
        elif status_enum == schemas.VideoStatus.PUBLISHED_PENDING:
            conditions.extend(
                [
                    models.Video.is_approved.is_(False),
                    review_status_subquery == models.ReviewStatus.PENDING.value,
                ]
            )
        elif status_enum == schemas.VideoStatus.PUBLISHED_REJECTED:
            conditions.extend(
                [
                    models.Video.is_approved.is_(False),
                    review_status_subquery == models.ReviewStatus.REJECTED.value,
                ]
            )

    if author_id:
        conditions.append(models.Video.author_id == author_id)
    if category_id:
        conditions.append(models.Video.category_id == category_id)
    if keyword:
        conditions.append(
            or_(
                models.Video.title.ilike(f"%{keyword}%"),
                models.Video.description.ilike(f"%{keyword}%"),
            )
        )
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            conditions.append(models.Video.created_at >= start_dt)
        except ValueError:
            raise HTTPException(
                status_code=400, detail="开始日期格式错误，请使用 YYYY-MM-DD"
            ) from None
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            conditions.append(models.Video.created_at <= end_dt)
        except ValueError:
            raise HTTPException(
                status_code=400, detail="结束日期格式错误，请使用 YYYY-MM-DD"
            ) from None

    # 执行查询
    where_clause = and_(*conditions) if conditions else None

    return await crud.video.get_multi_cursor(db=db, pagination=pagination, where=where_clause)


@router.put("/admin/{video_id}/status", response_model=schemas.VideoOut)
async def update_video_status_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    video_id: int,
    status: str,
    reason: str | None = None,
    request: Request = None,
) -> schemas.VideoOut:
    """更新视频状态（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    # 记录操作前的值
    old_values = {"status": video.status, "admin_note": getattr(video, "admin_note", None)}

    # 更新状态
    update_data = {"status": status}
    if reason:
        update_data["admin_note"] = reason

    updated_video = await crud.video.update(db=db, db_obj=video, obj_in=update_data)

    # 记录审计日志
    await AuditLogService.log_action(
        db=db,
        user=current_user,
        action=AuditAction.UPDATE,
        resource_type=AuditResourceType.VIDEO,
        resource_id=video_id,
        resource_name=video.title,
        description=f"更新视频状态为: {status}",
        reason=reason,
        old_values=old_values,
        new_values=update_data,
        request=request,
    )

    return updated_video


@router.post("/admin/batch-status")
async def batch_update_video_status_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    video_ids: list[int],
    status: str,
    reason: str | None = None,
    request: Request = None,
) -> dict:
    """批量更新视频状态（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    updated_count = 0
    updated_videos = []

    for video_id in video_ids:
        video = await crud.video.get(db=db, id=video_id)
        if video:
            # 记录操作前的值
            old_values = {"status": video.status, "admin_note": getattr(video, "admin_note", None)}

            update_data = {"status": status}
            if reason:
                update_data["admin_note"] = reason

            await crud.video.update(db=db, db_obj=video, obj_in=update_data)
            updated_count += 1
            updated_videos.append({"id": video_id, "title": video.title, "old_values": old_values})

    # 记录批量操作审计日志
    if updated_videos:
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BATCH_UPDATE,
            resource_type=AuditResourceType.VIDEO,
            description=f"批量更新 {updated_count} 个视频状态为: {status}",
            reason=reason,
            new_values={"status": status, "video_ids": video_ids, "updated_count": updated_count},
            request=request,
        )

    return {"message": f"成功更新 {updated_count} 个视频状态", "updated_count": updated_count}


# ==================== 通用视频操作接口 ====================
# 注意：动态路由 /{video_id} 必须放在所有具体路径之后


@router.get("/{video_id}", response_model=VideoOut)
@limiter.limit("100/minute")
async def read_video(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取单个视频的完整聚合信息 (重构版)
    - 返回 VideoOut 模型，包含作者、统计等所有信息
    - 权限控制
    """
    # 1. 权限检查并获取视频对象
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在或无权访问")

    # 2. 调用聚合服务获取完整视频信息
    video_out = await video_aggregation_service.get_aggregated_video(
        db=db, video_id=video_id, current_user=current_user
    )

    if not video_out:
        # 理论上不应发生，因为我们已经通过了权限检查
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="无法获取视频聚合信息")

    return video_out


@router.put("/{video_id}", response_model=schemas.VideoOut)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> Any:
    """更新视频（使用编排服务）"""
    video = await video_folder_service.update_video(
        db=db, video_id=video_id, obj_in=video_in, current_user=current_user
    )
    return video


@router.delete("/{video_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> None:
    """删除视频（使用编排服务）"""
    await video_folder_service.delete_video(db=db, video_id=video_id, current_user=current_user)


@router.delete("/admin/{video_id}", response_model=dict)
async def delete_video_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    confirm: bool = Body(False, description="确认删除，false时返回确认信息，true时执行删除"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> dict:
    """删除视频（管理员）- 带确认机制

    **删除流程**：
    1. 首次调用（confirm=false）：返回视频详情和影响范围，供前端展示确认对话框
    2. 二次调用（confirm=true）：执行实际删除操作

    **权限要求**：需要视频管理权限（VIDEO.MANAGE.ALL）
    """
    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取视频
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")

    # 第一步：返回确认信息
    if not confirm:
        # 统计关联数据
        from sqlalchemy import func

        # 统计评论数
        comment_count = await db.scalar(
            select(func.count(models.Comment.id)).where(
                models.Comment.content_type == "video",
                models.Comment.content_id == video_id,
                models.Comment.is_deleted.is_(False),
            )
        )

        # 统计点赞数
        like_count = await db.scalar(
            select(func.count(models.Like.id)).where(
                models.Like.content_type == "video",
                models.Like.content_id == video_id,
                models.Like.is_active.is_(True),
            )
        )

        # 统计收藏数
        favorite_count = await db.scalar(
            select(func.count(models.Favorite.id)).where(
                models.Favorite.content_type == "video",
                models.Favorite.content_id == video_id,
                models.Favorite.is_active.is_(True),
            )
        )

        return {
            "message": "请确认删除操作",
            "video": {
                "id": video.id,
                "title": video.title,
                "author_id": video.author_id,
                "created_at": video.created_at.isoformat() if video.created_at else None,
                "visit_count": video.visit_count,
                "duration": video.duration,
            },
            "impact": {
                "comment_count": comment_count or 0,
                "like_count": like_count or 0,
                "favorite_count": favorite_count or 0,
            },
            "warning": "删除后数据无法恢复，相关的评论、点赞、收藏等数据也将被删除",
            "next_step": "请在请求体中设置 confirm=true 来确认删除",
        }

    # 第二步：执行删除
    logger.info(f"管理员 {current_user.id} 正在删除视频 {video_id} (标题: {video.title})")

    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="video", content_id=video.id)
    if review:
        await crud.review.remove(db=db, id=review.id)

    # 软删除视频
    await crud.video.soft_remove(db=db, id=video_id)

    # 更新用户视频数
    await crud.user_stats.increment(db=db, user_id=video.author_id, field="video_count", value=-1)

    logger.info(f"视频 {video_id} 已被管理员 {current_user.id} 删除")

    return {
        "message": "视频删除成功",
        "video_id": video_id,
        "deleted_by": current_user.id,
    }


@router.post("/{video_id}/progress", response_model=dict)
async def update_video_progress(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    progress_seconds: int = Query(..., description="播放进度（秒）"),
    total_duration: int = Query(..., description="视频总时长（秒）"),
    current_user: models.User = Depends(deps.get_current_active_user),
    behavior_tracking_service: BehaviorTrackingService = Depends(get_behavior_tracking_service),
) -> Any:
    """更新视频播放进度

    **功能说明**：
    - 记录用户的视频播放进度
    - 支持断点续播功能
    - 自动计算播放完成率
    - 使用专门的进度表优化性能
    """
    # 检查视频是否存在
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在")

    # 使用新的进度表更新进度（UPSERT操作）
    from app.crud.video_progress import video_progress

    progress_record = await video_progress.upsert_progress(
        db=db,
        user_id=current_user.id,
        video_id=video_id,
        progress_seconds=progress_seconds,
        total_duration=total_duration,
    )

    # 优化行为追踪：只在特定条件下记录到 user_interactions
    # 减少数据量：每10秒或进度变化超过5%才记录一次行为
    should_track_behavior = (
        progress_seconds % 10 == 0  # 每10秒记录一次
        or progress_record.completion_rate >= 0.9  # 完成时记录
        or progress_seconds <= 10  # 开始观看时记录
    )

    if should_track_behavior:
        await behavior_tracking_service.track_interaction(
            db=db,
            user_id=current_user.id,
            content_type="video",
            content_id=video_id,
            interaction_type="watch_progress",
            extra_data={
                "progress_seconds": progress_seconds,
                "total_duration": total_duration,
                "completion_rate": progress_record.completion_rate,
            },
        )

    return {
        "message": "播放进度已更新",
        "progress_seconds": progress_record.progress_seconds,
        "completion_rate": progress_record.completion_rate,
        "is_completed": progress_record.is_completed_bool,
    }


@router.get("/{video_id}/progress", response_model=dict)
async def get_video_progress(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频播放进度"""
    # 检查视频是否存在
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在")

    # 从新的进度表查询进度
    from app.crud.video_progress import video_progress

    progress_record = await video_progress.get_user_video_progress(
        db=db, user_id=current_user.id, video_id=video_id
    )

    if not progress_record:
        return {
            "progress_seconds": 0,
            "completion_rate": 0.0,
            "is_completed": False,
            "last_watched": None,
        }

    return {
        "progress_seconds": progress_record.progress_seconds,
        "completion_rate": progress_record.completion_rate,
        "is_completed": progress_record.is_completed_bool,
        "last_watched": progress_record.updated_at,
    }
