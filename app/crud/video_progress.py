"""视频进度CRUD操作"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.dialects.postgresql import insert

from app.crud.base import CRUDBase
from app.models.video_progress import VideoProgress
from app.schemas.video_progress import VideoProgressCreate, VideoProgressUpdate


class CRUDVideoProgress(CRUDBase[VideoProgress, VideoProgressCreate, VideoProgressUpdate]):
    """视频进度CRUD操作"""
    
    async def get_user_video_progress(
        self, 
        db: AsyncSession, 
        *, 
        user_id: int, 
        video_id: int
    ) -> Optional[VideoProgress]:
        """获取用户特定视频的播放进度"""
        result = await db.execute(
            select(VideoProgress).where(
                VideoProgress.user_id == user_id,
                VideoProgress.video_id == video_id
            )
        )
        return result.scalar_one_or_none()
    
    async def upsert_progress(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        video_id: int,
        progress_seconds: int,
        total_duration: int
    ) -> VideoProgress:
        """插入或更新视频播放进度（PostgreSQL UPSERT）"""
        completion_rate = min(progress_seconds / total_duration, 1.0) if total_duration > 0 else 0
        is_completed = 1 if completion_rate >= 0.9 else 0
        
        # 使用 PostgreSQL 的 ON CONFLICT 语法实现 UPSERT
        stmt = insert(VideoProgress).values(
            user_id=user_id,
            video_id=video_id,
            progress_seconds=progress_seconds,
            total_duration=total_duration,
            completion_rate=completion_rate,
            is_completed=is_completed
        )
        
        # 冲突时更新除了 created_at 之外的所有字段
        stmt = stmt.on_conflict_do_update(
            constraint='uq_user_video_progress',
            set_=dict(
                progress_seconds=stmt.excluded.progress_seconds,
                total_duration=stmt.excluded.total_duration,
                completion_rate=stmt.excluded.completion_rate,
                is_completed=stmt.excluded.is_completed,
                updated_at=stmt.excluded.updated_at
            )
        )
        
        # 返回更新后的记录
        stmt = stmt.returning(VideoProgress)
        result = await db.execute(stmt)
        await db.commit()
        return result.scalar_one()
    
    async def get_user_completed_videos(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        limit: int = 100,
        offset: int = 0
    ) -> list[VideoProgress]:
        """获取用户已完成观看的视频列表"""
        result = await db.execute(
            select(VideoProgress)
            .where(
                VideoProgress.user_id == user_id,
                VideoProgress.is_completed == 1
            )
            .order_by(VideoProgress.updated_at.desc())
            .limit(limit)
            .offset(offset)
        )
        return result.scalars().all()
    
    async def get_user_watching_videos(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> list[VideoProgress]:
        """获取用户正在观看的视频列表（有进度但未完成）"""
        result = await db.execute(
            select(VideoProgress)
            .where(
                VideoProgress.user_id == user_id,
                VideoProgress.progress_seconds > 0,
                VideoProgress.is_completed == 0
            )
            .order_by(VideoProgress.updated_at.desc())
            .limit(limit)
            .offset(offset)
        )
        return result.scalars().all()
    
    async def get_video_completion_stats(
        self,
        db: AsyncSession,
        *,
        video_id: int
    ) -> dict:
        """获取视频的完成度统计"""
        from sqlalchemy import func, case
        
        result = await db.execute(
            select(
                func.count(VideoProgress.id).label('total_viewers'),
                func.count(case((VideoProgress.is_completed == 1, 1))).label('completed_viewers'),
                func.avg(VideoProgress.completion_rate).label('avg_completion_rate'),
                func.avg(VideoProgress.progress_seconds).label('avg_watch_time')
            ).where(VideoProgress.video_id == video_id)
        )
        
        stats = result.first()
        return {
            'total_viewers': stats.total_viewers or 0,
            'completed_viewers': stats.completed_viewers or 0,
            'completion_rate': float(stats.completed_viewers or 0) / max(stats.total_viewers or 1, 1),
            'avg_completion_rate': float(stats.avg_completion_rate or 0),
            'avg_watch_time': float(stats.avg_watch_time or 0)
        }


# 创建CRUD实例
video_progress = CRUDVideoProgress(VideoProgress)
