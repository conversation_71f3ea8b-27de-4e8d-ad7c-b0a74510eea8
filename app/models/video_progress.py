"""视频播放进度模型"""

from datetime import datetime, UTC
from sqlalchemy import Column, Integer, Float, ForeignKey, UniqueConstraint, Index, Timestamp
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class VideoProgress(Base):
    """视频播放进度模型 - 每个用户每个视频只保存最新进度"""
    
    __tablename__ = "video_progress"
    
    # 添加复合唯一约束和索引
    __table_args__ = (
        UniqueConstraint('user_id', 'video_id', name='uq_user_video_progress'),
        Index('ix_video_progress_user_id', 'user_id'),
        Index('ix_video_progress_video_id', 'video_id'),
        Index('ix_video_progress_updated_at', 'updated_at'),
        {"comment": "视频播放进度表"}
    )
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    video_id = Column(Integer, ForeignKey("videos.id", ondelete="CASCADE"), nullable=False)
    
    # 进度信息
    progress_seconds = Column(Integer, nullable=False, default=0, comment="播放进度（秒）")
    total_duration = Column(Integer, nullable=False, default=0, comment="视频总时长（秒）")
    completion_rate = Column(Float, nullable=False, default=0.0, comment="完成率 0.0-1.0")
    
    # 状态标记
    is_completed = Column(Integer, nullable=False, default=0, comment="是否完成观看（90%以上算完成）")
    
    # 时间戳
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=False)
    updated_at = Column(Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=False)
    
    # 关联关系
    user = relationship("User")
    video = relationship("Video")
    
    def __repr__(self):
        return f"<VideoProgress user_id={self.user_id} video_id={self.video_id} progress={self.progress_seconds}s>"
    
    @property
    def is_completed_bool(self) -> bool:
        """返回是否完成观看的布尔值"""
        return self.completion_rate >= 0.9
    
    def update_progress(self, progress_seconds: int, total_duration: int):
        """更新进度信息"""
        self.progress_seconds = progress_seconds
        self.total_duration = total_duration
        self.completion_rate = min(progress_seconds / total_duration, 1.0) if total_duration > 0 else 0
        self.is_completed = 1 if self.completion_rate >= 0.9 else 0
        self.updated_at = datetime.now(UTC)
