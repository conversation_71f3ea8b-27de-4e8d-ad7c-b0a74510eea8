"""视频进度相关的Pydantic模型"""

from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict


class VideoProgressBase(BaseModel):
    """视频进度基础模型"""
    progress_seconds: int = Field(..., ge=0, description="播放进度（秒）")
    total_duration: int = Field(..., gt=0, description="视频总时长（秒）")


class VideoProgressCreate(VideoProgressBase):
    """创建视频进度模型"""
    video_id: int = Field(..., description="视频ID")


class VideoProgressUpdate(VideoProgressBase):
    """更新视频进度模型"""
    pass


class VideoProgressOut(VideoProgressBase):
    """视频进度输出模型"""
    id: int = Field(..., description="进度记录ID")
    user_id: int = Field(..., description="用户ID")
    video_id: int = Field(..., description="视频ID")
    completion_rate: float = Field(..., ge=0, le=1, description="完成率 0.0-1.0")
    is_completed: bool = Field(..., description="是否完成观看")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    model_config = ConfigDict(from_attributes=True)


class VideoProgressSummary(BaseModel):
    """视频进度摘要模型"""
    progress_seconds: int = Field(..., description="播放进度（秒）")
    completion_rate: float = Field(..., description="完成率")
    is_completed: bool = Field(..., description="是否完成观看")
    last_watched: datetime | None = Field(None, description="最后观看时间")


class VideoCompletionStats(BaseModel):
    """视频完成度统计模型"""
    video_id: int = Field(..., description="视频ID")
    total_viewers: int = Field(..., description="总观看人数")
    completed_viewers: int = Field(..., description="完成观看人数")
    completion_rate: float = Field(..., description="完成率")
    avg_completion_rate: float = Field(..., description="平均完成度")
    avg_watch_time: float = Field(..., description="平均观看时长（秒）")
